"use client";

import axios from "axios";
import { createContext } from "react"

type Props = {
    children : React.ReactNode
}

type ContextType={
    
}
export const InfoContainer = createContext<ContextType|null>(null);

export default function ContextProvider({children}:Props){
    
    const information : ContextType = {}
    return(
        <>
        <InfoContainer.Provider value={information}>
            {children}
        </InfoContainer.Provider>
        </>
    )
}