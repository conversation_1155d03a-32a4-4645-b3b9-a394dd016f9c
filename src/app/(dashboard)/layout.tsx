"use client";

import { useState } from "react";
import Header from "./header/header"
import Sidebar from "./Sidebar/sidebar"

type Children = {
    children: React.ReactNode
}

export default function DashboardLayout({children}:Children){
    const [sidebarCollapse,setSidebarCollapse] = useState<boolean>(true);
    return(
        <>
        <header>
            <Header
            setSidebarCollapse={setSidebarCollapse}
            />
        </header>

        <section>
            <div className="fixed top-[60px] w-full h-full grid grid-cols-10 bg-[#F6F9FF]">
                <div className={`transition-all duration-300 ease-out bg-white overflow-y-auto customScrollbar ${sidebarCollapse?"col-span-2":"absolute -translate-x-full w-0"}`}>
                    <Sidebar/>
                </div>

                <div className={` overflow-y-auto ${sidebarCollapse?"col-span-8":"col-span-10"}`}>
                    {children}
                </div>
            </div>
        </section>
        </>
    )
}