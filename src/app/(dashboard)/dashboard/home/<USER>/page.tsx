"use client";

import DashLoading from "@/app/(dashboard)/loading";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { <PERSON>, <PERSON>rop<PERSON> } from "next/font/google"
import Image from "next/image";
import { useState } from "react";
import { TbCaptureFilled } from "react-icons/tb";

const anton = Anton({
    subsets:['latin'],
    weight:'400'
});

const manrope = Manrope({
    subsets:['latin']
});

type Content = {
    title:string | null,
    banner_text:string | null,
    imgpath:File | null,
    preImgPath:string|null
}

type TxtWarning = {
    title:string | null,
    context:string | null
}

type CountConent = {
    title:number | null,
    context: number | null
}

export default function Banner(){
    const [content,setContent] = useState<Content>({
        title:null,
        banner_text:null,
        imgpath:null,
        preImgPath:null
    });
    
    const [imgPreview,setImgPreview] = useState<string | null>();

    const [txtWarning,setTxtWarning] = useState<TxtWarning>({
        title:null,
        context:null
    });

    const [countContent,setCountContent] = useState<CountConent>({
        title:0,
        context:0
    })

    const queryClient = useQueryClient();

    const {isPending,isError,data} = useQuery({
        queryKey:["bannerRetrieveData"],
        queryFn:async ()=>{
            const response = await axios("/api/bannerretrievedata");
            const result = response.data?.data[0] || "";
            
            setContent({...content,title:result.title,banner_text:result.banner_text,imgpath:result.imgpath,preImgPath:result.imgpath});

            setImgPreview(result.imgpath)
            return result;
        }
    });

    const bannerAddData = useMutation({
        mutationFn:async (formData:FormData)=>{
            await axios.post('/api/banneradddata',formData)
                .then((res)=>{console.log(res)})
        },
        onSuccess:()=>{
            queryClient.invalidateQueries({queryKey:['bannerRetrieveData']})
        }
    })

    const inputChange=(event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>)=>{
        const {name,value} = event.target;

        if(name == "title"){
            const step1 = value.split(" ");
            const step2 = step1.length;

            setCountContent({...countContent,title:step2});
            if(step2 > 6){
                const step3 = step1.slice(0, 6).join(" ");

                setContent({...content,title:step3});
                setTxtWarning({...txtWarning,title:"not more than 6 words"});
            }else{
                const step3 = step1.slice(0, step2).join(" ");

                setContent({...content,title:step3});
                setTxtWarning({...txtWarning,title:null});
            }
        }

        if(name == "banner_text"){
            const step1 = value.split(" ");
            const step2 = step1.length;

            setCountContent({...countContent,context:step2});

            if(step2 > 22){
                const step3 = step1.slice(0, 22).join(" ");

                setContent({...content,banner_text:step3});
                setTxtWarning({...txtWarning,context:"not more than 22 words"});
            }else{
                const step3 = step1.slice(0, step2).join(" ");

                setContent({...content,banner_text:step3});
                setTxtWarning({...txtWarning,context:null})
            }
        }

        if(event.target instanceof HTMLInputElement && event.target.type == "file"){
            const file = event.target.files;

            if(file && file.length > 0){
                const imgUrl = URL.createObjectURL(file[0]);

                setImgPreview(imgUrl);
                setContent({...content,imgpath:file[0]})
            }
        }
    }

    const formHandler=(event: { preventDefault: () => void; })=>{
        event.preventDefault();
        const formData = new FormData();
        
        Object.entries(content).forEach(([key,value])=>{
            if(value !== null && value !== undefined){
                if(value instanceof File){
                    formData.append(key,value)
                }else{
                    formData.append(key,value.toString())
                }
            }
        });

        bannerAddData.mutate(formData);
    }
    return(
        <>
        <section className="mt-20">
            {
                isPending?
                <div className="h-screen w-full flex justify-center items-center">
                    <DashLoading/>
                </div>:
                isError?
                <div>
                    <h1>
                        Something went wrong
                    </h1>
                </div>:
                <form onSubmit={formHandler}>
            <div className="grid grid-cols-2 items-start px-5 py-15">
                <div className="space-y-7 relative w-full after:absolute after:content-[''] after:h-[20%] after:w-0.5 after:bg-black after:right-[14%] after:top-[30%]">
                    <div className="pr-[25%]">
                        <h2 className={`${anton.className} font-bold text-3xl uppercase`}>
                            title
                        </h2>

                        <div className="mt-5 h-[200px] w-full shadow-sm p-2">
                            <textarea className={`${anton.className} font-bold xl:text-5xl text-4xl text-black uppercase h-full w-full bg-white rounded-lg p-2 resize-none customScrollbar`} name="title" value={content.title || ""} onChange={inputChange}>
                                
                            </textarea>
                        </div>
                        <div className={`${manrope.className} font-semibold flex flex-row justify-end mt-2`}>
                            {
                                txtWarning.title?
                                <span className="text-rose-500">
                                    {txtWarning.title}
                                </span>
                                :
                                <>
                                <span>
                                {countContent.title}
                            </span>
                            <span>
                                /
                            </span>
                            <span>
                                6
                            </span>
                                </>
                            }
                        </div>
                    </div>

                    <div className="pr-[25%]">
                        <h2 className={`${anton.className} font-bold text-3xl uppercase`}>
                            context
                        </h2>
                        <div className="mt-5 h-40 w-full shadow-sm p-2">
                            <textarea className={`${manrope.className} font-normal xl:text-base text-sm leading-[22px] text-[#000000]/60 h-full w-full bg-white p-2`} name="banner_text" onChange={inputChange} value={content.banner_text || ""}></textarea>
                        </div>

                        <div className={`${manrope.className} font-semibold flex flex-row justify-end mt-2`}>
                            {
                                txtWarning.context?
                                <span className="text-rose-500">
                                    {txtWarning.context}
                                </span>
                                :
                                <>
                                <span>
                                {countContent.context}
                            </span>
                            <span>
                                /
                            </span>
                            <span>
                                22
                            </span>
                                </>
                            }
                        </div>
                    </div>

                    <div className="flex flex-row gap-x-5">
                        <button type="submit" className={`${manrope.className} bg-sky-500 py-2 px-5 rounded-xl text-white capitalize font-semibold transition-all duration-200 ease-linear hover:bg-sky-800`}>
                            update
                        </button>

                        <button type="button" className={`${manrope.className} bg-yellow-500 py-2 px-5 rounded-xl text-white capitalize font-semibold transition-all duration-200 ease-linear hover:bg-yellow-800`} onClick={()=>{
                            setContent({...content,title:null,banner_text:null,imgpath:null})
                            setImgPreview(null)
                            }}>
                            clear
                        </button>
                    </div>
                </div>

                <div>
                    <div className="xl:h-[650px] h-full xl:w-[450px] w-full shadow-lg relative overflow-hidden rounded-lg">
                        {
                            imgPreview?
                            <>
                            <div className="absolute top-0 left-0 h-full w-full z-10">
                                <Image src={imgPreview} alt="bannerImg" height={650} width={450} className="h-full w-full"/>
                            </div>
                            <label htmlFor="imgPath" className="absolute h-full w-full top-0 left-0 flex justify-center items-center group transition-all duration-200 ease-linear z-20">
                            <input type="file" name="imgPath" id="imgPath" className="h-full w-full hidden" accept="image/*" onChange={inputChange}/>

                            <span className="text-6xl group-hover:scale-110 group-hover:text-white transition-all duration-200 ease-linear">
                                <TbCaptureFilled />
                            </span>
                        </label>
                            </>
                            :
                            <label htmlFor="imgPath" className="absolute h-full w-full top-0 left-0 bg-white flex justify-center items-center group hover:bg-[#34495e]/20 transition-all duration-200 ease-linear">
                            <input type="file" name="imgPath" id="imgPath" className="h-full w-full hidden" accept="image/*" onChange={inputChange}/>

                            <span className="text-6xl group-hover:scale-110 group-hover:text-white transition-all duration-200 ease-linear">
                                <TbCaptureFilled />
                            </span>
                        </label>
                        }
                        
                    </div>
                </div>
            </div>
            </form>
            }
            
        </section>
        </>
    )
}