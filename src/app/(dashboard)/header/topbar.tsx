import Link from "next/link";
import { <PERSON><PERSON><PERSON>,<PERSON>, Open_Sans } from "next/font/google";
import { FiMenu } from "react-icons/fi";
import { IoSearchOutline } from "react-icons/io5";
import { IoMdArrowDropdown, IoMdNotificationsOutline } from "react-icons/io";
import DemoProfile from "../../../../public/demoprofile.jpg";
import Image from "next/image";
import { Props } from "./header";

const nunito = Nunito({
    subsets:['latin']
});

const anton = Anton({
    subsets:['latin'],
    weight:'400'
});

const openSans = Open_Sans({
    subsets:['latin']
});

export default function Topbar({setSidebarCollapse}:Props){
    return(
        <>
        <nav className="fixed top-0 left-0 w-full h-[60px] grid grid-cols-10 items-center px-10 bg-white z-50 shadow-lg">
            <div className="col-span-2">
                <Link href="/" className={`${anton.className} font-bold text-[26px] text-black uppercase`}>
                Shop.co
                </Link>
            </div>

            <div className="col-span-6 flex flex-row gap-x-5">
                <div>
                    <button className="text-[32px] text-black hover:cursor-pointer" onClick={()=>{
                        setSidebarCollapse(prev => !prev);
                    }}>
                        <FiMenu />
                    </button>
                </div>

                <div className="relative h-[37px] w-[320px] flex items-center">
                    <span className="absolute h-full w-full">
                        <input type="text" className={`h-full w-full pr-[38px] pl-[7px] ${nunito.className} text-base font-medium border border-gray-300/50 rounded-sm`} placeholder="Search" />
                    </span>
                    <span className="absolute right-2">
                        <IoSearchOutline />
                    </span>
                </div>
            </div>

            <div className="col-span-2 flex flex-row items-center justify-between">
                <div>
                    <span className="text-2xl">
                        <IoMdNotificationsOutline />
                    </span>
                </div>

                <div>
                    <Image src={DemoProfile} alt="profile" className="h-9 w-9 rounded-full border border-gray-300"/>
                </div>

                <div>
                    <h5 className={`${openSans.className} text-sm font-bold capitalize text-black`}>
                        sure name
                    </h5>
                </div>

                <div>
                    <IoMdArrowDropdown />
                </div>
            </div>
        </nav>
        </>
    )
}