import { NextResponse } from "next/server";
import { PrismaClient } from "../../../../generated/prisma";

const prisma = new PrismaClient();

export async function GET() {
    try {
        // Test database connection
        await prisma.$connect();
        
        // Try to fetch existing sponsor images
        const sponserImages = await prisma.sponserimg.findMany();
        
        return NextResponse.json({
            message: "Database connection successful",
            count: sponserImages.length,
            data: sponserImages
        });
        
    } catch (error) {
        console.error("Database connection error:", error);
        return NextResponse.json(
            { 
                error: "Database connection failed", 
                details: error instanceof Error ? error.message : "Unknown error" 
            }, 
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}
