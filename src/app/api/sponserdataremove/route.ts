import fs from "fs";
import { PrismaClient } from "../../../../generated/prisma";
import { NextResponse } from "next/server";

const prisma = new PrismaClient();

export async function DELETE(req:Request){
    const {searchParams} = new URL(req.url);
    const id = searchParams.get("id");
    const imgpath = searchParams.get("imgpath");

    try{
        if(typeof imgpath == "string"){
            fs.unlink("public/" + imgpath,(err)=>{
                console.log(err)
            })
        }

        await prisma.sponserimg.delete({
            where:{
                id: Number(id)
            }
        });

        return NextResponse.json({message:"remove"},{status:200})
    }catch(error){
        return NextResponse.json(error)
    }
}