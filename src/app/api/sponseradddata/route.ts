import { mkdir, writeFile } from "fs/promises";
import { NextRequest, NextResponse } from "next/server";
import path from "path";
import { PrismaClient } from "../../../../generated/prisma";

const prisma = new PrismaClient();

export async function POST(req:NextRequest){
    const formData = await req.formData();
    const existFile= formData.get('imgpath');

    const filename = existFile instanceof File? existFile.name:null;
    const splitName= filename?.split(".") ?? "";
    const suffixName= Date.now();   
    const renameFile= splitName[0] + suffixName;
    const joinFileFormat = renameFile + "." + splitName[1];

    const uploadDir = path.join(process.cwd(),"public/assets");
    const buffer = existFile instanceof File? Buffer.from(await existFile.arrayBuffer()):"no file received";

    try{
        const response = NextResponse.json({message:"success"},{status:200});

        await mkdir(uploadDir,{recursive:true});

        await writeFile(path.join(process.cwd(), "public/assets/" + joinFileFormat),buffer);

        await prisma.sponserimg.create({
            data:{imgpath:"/assets/" + joinFileFormat}
        });

        return NextResponse.json({message:"success"},{status:200})
    }catch(error){
        return NextResponse.json(error);
    }
}