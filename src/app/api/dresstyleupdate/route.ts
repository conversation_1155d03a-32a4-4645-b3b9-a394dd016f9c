import { NextRequest, NextResponse } from "next/server";
import { mkdir, writeFile, unlink } from "fs/promises";
import path from "path";
import fs from "fs";
import { PrismaClient } from "../../../../generated/prisma";

const prisma = new PrismaClient();

export async function PUT(req:NextRequest){
    const formData = await req.formData();
    
    // collect the formData
    const container = [...formData.entries()];

    // convert to array of object
    const reFormateObj = container.reduce((acc,[key,value])=>{
        const match = key.match(/([a-zA-Z_]+)|(\d+)/g);

        if(!match) return acc;

        const [field,indexStr] = match;
        const index = parseInt(indexStr, 10);

        if(!acc[index]){
            acc[index] = {};
        }

        acc[index][field] = value;

        return acc;
    },[] as Record<string, FormDataEntryValue>[]);

    // place & remove file base on needs
    const structuredArr = await Promise.all(reFormateObj.map(async (items,index)=>{
        if(items.currentImg != "null"){
            // config the file
            const fileName = items.currentImg instanceof File ? items.currentImg.name : null;
            const separate = fileName?.split(".");
            const imgName = separate?.[0];
            const renameImg= (imgName ? imgName : "") + Date.now();
            const addFormat= renameImg + "." + separate?.[1];

            // buffer file
            const arrayBuffer = items.currentImg instanceof File ? await items.currentImg.arrayBuffer() : null;
            const buffer = arrayBuffer ? Buffer.from(arrayBuffer) : "";

            // place it to public folder
            const uploadDir = path.join(process.cwd(),"public/assets/");

            await mkdir(uploadDir,{recursive:true});

            await writeFile(path.join(process.cwd(),"public/assets/" + addFormat),buffer)

            if(items.previousImg != "null"){
                fs.unlink("public/assets/" + items.previousImg,(err)=>{
                console.log(err)
            })
            }

            return {
                id: items.id,
                title: typeof items.title === "string" ? items.title : null,
                colstart: typeof items.colstart === "string" ? items.colstart : null,
                colspan : typeof items.colspan === "string" ? items.colspan : null,
                imgpath : addFormat
            }
        }else{
            return {
                id: items.id,
                title: typeof items.title === "string" ? items.title : null,
                colstart: typeof items.colstart === "string" ? items.colstart : null,
                colspan : typeof items.colspan === "string" ? items.colspan : null,
                imgpath : typeof items.previousImg === "string" ? items.previousImg : null
            }
        }
    })
    )
    
    await Promise.all(structuredArr.map(async (items)=>{
        await prisma.dresstyle.update({
            where: {id : Number(items.id)},
            data: {
                title : items.title,
                imgpath: items.imgpath,
                colstart : items.colstart,
                colspan : items.colspan
            }
        })
    }));


    return NextResponse.json({message:"update successfully"},{status:200})
}