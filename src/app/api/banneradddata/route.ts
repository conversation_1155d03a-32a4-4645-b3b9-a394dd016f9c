import { mkdir, writeFile } from "fs/promises";
import { NextRequest, NextResponse } from "next/server";
import path from "path";
import fs from "fs";
import { PrismaClient } from "../../../../generated/prisma";

const prisma = new PrismaClient();

export async function POST(req:NextRequest){
    const formData = await req.formData();
    // check if there is any prevalue for image
    const preImgPath = formData.get('preImgPath');
    // check file existence
    const existFile = formData.get('imgpath');
    const filename = existFile instanceof File? existFile.name : null;
    // rename the file for unique 
    const splitName= filename?.split(".") ?? "";
    const suffixName= Date.now();   
    const renameFile= splitName[0] + suffixName;
    const joinFileFormat = renameFile + "." + splitName[1];
    // place the file to public folder
    const uploadDir = path.join(process.cwd(),"public/assets");
    const buffer = existFile instanceof File? Buffer.from(await existFile.arrayBuffer()):"no file received";
    // recollect data from formdata
    let retireveFormData:{[key:string]:string} = {};

    formData.forEach((value,key)=>{
        if(typeof value === "string"){
            if(key == "preImgPath"){
                retireveFormData;
            }else{
                retireveFormData[key] = value;
            }
        }
    })
    try{
        if(existFile != preImgPath){
            
            await mkdir(uploadDir,{recursive:true});

            await writeFile(path.join(process.cwd(), "public/assets/" + joinFileFormat),buffer);

            if(typeof preImgPath == "string"){
                fs.unlink("public/" + preImgPath,(err)=>{
                    console.log(err)
                })
            }
        }

        await prisma.bannerdata.deleteMany({});

        await prisma.bannerdata.create({
            data:{
                ...retireveFormData,
                imgpath: existFile != preImgPath? "/assets/" + joinFileFormat : retireveFormData.imgpath
            }
        })
        return NextResponse.json({message:"complete"})
    }catch(error){
        return NextResponse.json(error);
    }
}