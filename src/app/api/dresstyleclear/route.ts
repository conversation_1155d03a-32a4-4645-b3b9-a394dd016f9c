import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "../../../../generated/prisma";
import fs from "fs";

const prisma = new PrismaClient();

export async function DELETE(req:NextRequest){
    const data = await req.json();

    // remove all image from public folder
    await Promise.all(data.map(async (items: string)=>{
        fs.unlink("public/assets/" + items,(err)=>{
            console.log(err)
        })
    }));

    // remove all data from database
    await prisma.dresstyle.deleteMany({});

    return NextResponse.json(null,{status:204});
}