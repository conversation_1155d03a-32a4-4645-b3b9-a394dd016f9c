import {Manrope} from 'next/font/google';
import Link from "next/link";
import { RxCross1 } from 'react-icons/rx';

const manrope = Manrope({
    subsets: ['latin']
})

export default function Offer(){
    return(
        <>
        <section className="bg-black">
            <div className="max-w-screen-2xl mx-auto xl:px-12 py-[19px] grid xl:grid-cols-3 grid-cols-1 items-center">
                <div className='col-span-2 xl:text-end text-center'>
                    <h5 className={`${manrope.className} text-white xl:text-sm text-xs capitalize tracking-wider`}>
                    sign up and get 20% off to your first order.
                    <Link href="#" className='underline ml-1'>sign up now</Link>
                    </h5>
                </div>

                <div className='xl:flex hidden justify-end text-white text-end'>
                    <RxCross1 />
                </div>
            </div>
        </section>
        </>
    )
}