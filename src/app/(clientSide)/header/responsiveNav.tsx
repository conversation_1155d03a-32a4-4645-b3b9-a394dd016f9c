import Link from "next/link";
import { <PERSON> } from "next/font/google";
import { IoCartOutline, IoMenu, IoSearch } from "react-icons/io5";
import { LuCircleUser } from "react-icons/lu";

const anton = Anton({
    subsets: ['latin'],
    weight: "400"
})

export default function ResponsiveNav(){
    return(
        <>
        <nav className="w-full h-16 px-5 grid grid-cols-2 justify-between items-center lg:hidden">
            <div className="flex flex-row gap-x-[18px] items-center">
                <span className="text-xl">
                    <IoMenu />
                </span>
                <span>
                    <Link href="#" className={`${anton.className} font-bold text-[25px] uppercase`}>Shop.co</Link>
                </span>
            </div>

            <div className="flex flex-row justify-end gap-x-[13.88px]">
                <span className="text-2xl">
                    <IoSearch />
                </span>
                <span className="text-2xl">
                    <IoCartOutline />
                </span>
                <span className="text-2xl">
                     <LuCircleUser />
                </span>
            </div>
        </nav>
        </>
    )
}