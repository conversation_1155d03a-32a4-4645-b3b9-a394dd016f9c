import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google";
import { FaFacebookF, FaTwitter } from "react-icons/fa"
import { IoLogoGithub } from "react-icons/io5"
import { RiInstagramFill } from "react-icons/ri"
import { platform } from "os";

const footerContent = [
    {
        title:'company',
        contentLink:[
            {title:'about',link:"#"},
            {title:'feature',link:"#"},
            {title:'works',link:"#"},
            {title:'career',link:"#"}
        ]
    },
    {
        title:'help',
        contentLink:[
            {title:'customer support',link:"#"},
            {title:'delivary details',link:"#"},
            {title:'terms & condition',link:"#"},
            {title:'privacy policy',link:"#"}
        ]
    },
    {
        title:'faq',
        contentLink:[
            {title:'account',link:"#"},
            {title:'manage deliveries',link:"#"},
            {title:'orders',link:"#"},
            {title:'payments',link:"#"}
        ]
    },
    {
        title:'resources',
        contentLink:[
            {title:'facebooks',link:"#"},
            {title:'development tutorial',link:"#"},
            {title:'how to blog',link:"#"},
            {title:'youtube playlist',link:"#"}
        ]
    }
]

const socialLink = [
    {platform:<FaTwitter />,link:"www.twitter.com"},
    {platform:<FaFacebookF />,link:"www.facebook.com"},
    {platform:<RiInstagramFill />,link:"www.instagram.com"},
    {platform:<IoLogoGithub />,link:"www.github.com"}
]

const anton = Anton({
    subsets:['latin'],
    weight:"400"
});

const manrope = Manrope({
    subsets:['latin']
})
export default function Info(){
    return(
        <>
        <section className="bg-[#F0F0F0] pt-[140px]">
            <div className="max-w-screen-2xl mx-auto xl:px-[148px] px-5 grid xl:grid-cols-6 grid-cols-1 gap-y-5 justify-between items-start pb-[50px]">
                <div className="col-span-2">
                    <h3 className={`${anton.className} font-bold text-black text-[32px] uppercase`}>
                        shop.co
                    </h3>
                    <div className="mt-[25px] xl:w-[248px]">
                        <p className={`${manrope.className} text-[#000000]/60 font-normal text-sm`}>
                            We have clothes that suits your style and which you're proud to wear. From women to men.
                        </p>
                    </div>
                    <div className="flex flex-row gap-x-3 mt-[35px]">
                        {socialLink.map((items,index)=>{
                            return <Link key={index} href={items.link} className="h-7 w-7 rounded-full flex justify-center items-center bg-white text-xs transition-all duration-300 ease-linear hover:bg-black hover:text-white">
                                {items.platform}
                            </Link>
                        })}
                    </div>
                </div>

                {
                    footerContent.map((items,index)=>{
                        return <div key={index} className="pt-[10px]">
                            <h4 className={`${manrope.className} text-base font-medium uppercase leading-[18px] tracking-[3px] text-black`}>
                                {items.title}
                            </h4>
                            
                            <ul className="flex flex-col gap-y-5 mt-[26px]">
                                {items.contentLink.map((subItems,subIndex)=>{
                                    return <li key={subIndex} className={`${manrope.className} font-normal text-base text-[#000000]/60 leading-[19px] capitalize`}>
                                        <Link href={subItems.link}>{subItems.title}</Link>
                                    </li>
                                })}
                            </ul>
                        </div>
                    })
                }
            </div>
        </section>
        </>
    )
}