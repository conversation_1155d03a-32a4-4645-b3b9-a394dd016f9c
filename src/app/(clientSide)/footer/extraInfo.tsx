import Image from "next/image";
import Link from "next/link";
import { Manrope } from "next/font/google";
import Visa from "../../../../public/visa.png";
import Payment from "../../../../public/payment.png";
import Paypal from "../../../../public/paypal.png";
import Applepay from "../../../../public/applepay.png";
import Googlepay from "../../../../public/googlepay.png";

const paymentLink = [
    {img:Visa,link:"#",alt:"visaImag"},
    {img:Payment,link:"#",alt:"paymentImg"},
    {img:Paypal,link:"#",alt:"paypalImg"},
    {img:Applepay,link:"#",alt:"applepay"},
    {img:Googlepay,link:"#",alt:"googlepay"}
];

const manrope = Manrope({
    subsets:['latin']
});
export default function ExtraInfo(){
    return(
        <>
        <section className="bg-[#F0F0F0]">
            <div className="max-w-screen-2xl mx-auto xl:px-[148px] px-5">
                <div className="grid xl:grid-cols-2 grid-cols-1 items-center gap-y-5 w-full border border-[#000000]/10 border-r-0 border-b-0 border-l-0 py-[25px]">
                    <div className="flex flex-row w-full xl:justify-start justify-center">
                    <h5 className={`${manrope.className} font-normal text-sm text-[#000000]/60 capitalize`}>
                        shop.co <span>&#169;</span> 2000-2023, all rights reserve
                    </h5>
                </div>

                <div className="flex flex-row w-full xl:justify-end justify-center">
                    {
                        paymentLink.map((items,index)=>{
                            return <Link key={index} href={items.link}>
                                <Image src={items.img} alt={items.alt}/>
                            </Link>
                        })
                    }
                </div>
                </div>
            </div>
        </section>
        </>
    )
}