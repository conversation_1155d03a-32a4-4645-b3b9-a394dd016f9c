import { <PERSON>,<PERSON><PERSON><PERSON> } from "next/font/google"
import { FaRegEnvelope } from "react-icons/fa";

const anton = <PERSON>({
    subsets: ['latin'],
    weight: "400"
});

const manrope = Manrope({
    subsets: ['latin']
});

export default function Contact(){
    return(
        <>
        <section className="translate-y-[90px]">
            <div className="max-w-screen-2xl mx-auto xl:px-[148px] px-5">
                <div className="grid xl:grid-cols-3 grid-cols-1 gap-y-5 bg-black py-[43px] xl:px-16 px-5 rounded-4xl">
                    <div className="xl:col-span-2 uppercase">
                        <h3 className={`${anton.className} text-white xl:text-[40px] text-2xl font-bold xl:pr-[285px]`}>
                           stay upto date about our latest offers 
                        </h3>
                    </div>

                    <div className="flex flex-col gap-y-[14px]">
                        <div className="w-full h-12 bg-white rounded-full relative">
                            <span className="absolute h-full w-full">
                                <input type="text" className={`${manrope.className} font-normal text-base text-[#000000]/40 h-full w-full placeholder:capitalize pl-[52px]`} placeholder="enter your email address" />
                            </span>
                            <span className="absolute text-[#000000]/40 top-[16.12px] left-[17.86px]">
                                <FaRegEnvelope />
                            </span>
                        </div>

                        <div>
                            <button className={`${manrope.className} font-medium text-base text-black capitalize bg-white w-full rounded-full py-3`}>
                                subscribe to newsletter
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        </>
    )
}