"use client";

import Image from "next/image";
import Sponser1 from "../../../../../public/sponser1.png";
import Sponser2 from "../../../../../public/sponser2.png";
import Sponser3 from "../../../../../public/sponser3.png";
import Sponser4 from "../../../../../public/sponser4.png";
import Sponser5 from "../../../../../public/sponser5.png";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import ClientLoading from "../../loading";
import { StaticImport } from "next/dist/shared/lib/get-img-props";
import { Key } from "react";

export default function Sponser(){
    const {isPending,isError,data} = useQuery({
        queryKey:["clientSponserImg"],
        queryFn:async()=>{
            const response = await axios("/api/clientsponsergetdata");
            const result = response.data.data;

            return result;
        }
    });
    
    return(
        <>
        <section className={`${isPending?"bg-white":"bg-black"} transition-all duration-150 ease-linear`}>
            <div className={`max-w-screen-2xl py-[61px] mx-auto xl:px-[148px] px-5 grid ${isPending?"grid-cols-1":"xl:grid-cols-5"} xl:gap-x-[106px] gap-y-5`}>
                {
                    isPending?
                    <div className="w-full py-5 flex justify-center items-cneter">
                        <ClientLoading/>
                    </div>:
                    isError?
                    <div className="w-full text-cnter">
                        <h4>
                            Something went wrong
                        </h4>
                    </div>:
                    data.map((items: { imgpath: string, },index: Key)=>{
                        return <span className="w-full h-9" key={index}>
                    <Image src={items.imgpath} alt="sponserImg" height={36} width={127} className="h-full w-full object-contain"/>
                </span>
                    })
                }
            </div>
        </section>
        </>
    )
}