"use client";
import Image from "next/image";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "next/font/google";
import Casual from "../../../../../public/casual.png";
import Formal from "../../../../../public/formal.png";
import Party from "../../../../../public/party.png";
import Gym from "../../../../../public/gym.png";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import ClientLoading from "../../loading";

type IntermediateObj = {
    title: string | null,
    imgpath: string | null,
    colspan: string | null,
    colstart: string | null
}

const manrope = Manrope({
    subsets:['latin'],
});

const anton = Anton({
    subsets:['latin'],
    weight:'400'
});

export default function BrowseDress(){
    const {isLoading,isError,data} = useQuery({
        queryKey:["getDresstyleData"],
        queryFn:async ()=>{
            const data = await axios.get("/api/getdresstyledata");
            const result: IntermediateObj[] = data.data;

            return result;
        }
    });
    return(
        <>
        <section>
            {
            isLoading?
            <div className="w-full py-5 flex justify-center items-center">
                <ClientLoading/>
            </div>:
            isError?
            <div className="w-full py-5">
                <h3>
                    error message will update soon
                </h3>
            </div>:
            <div className="max-w-screen-2xl mx-auto xl:px-[148px] px-5">
                <div className="w-full bg-[#F0F0F0] xl:px-16 px-5 py-[70px] rounded-lg">
                    <div className="w-full text-center mb-16">
                        <h3 className={`${anton.className} font-bold text-5xl uppercase text-black`}>
                            browse by dress style
                        </h3>
                    </div>

                    <div className="w-full grid xl:grid-cols-3 grid-cols-1 xl:gap-x-5 gap-y-5 mb-5">
                        {
                            data?.map((items,index)=>{
                                return <div className={`xl:${items.colstart} xl:${items.colspan} h-[289px] w-full rounded-lg bg-cover bg-center bg-no-repeat relative overflow-hidden`} key={index} style={{
                                    backgroundImage:`url("/assets/${items.imgpath}")`
                                }}>
                                {/* <Image src={items.imgpath != "null" ? `/assets/${items.imgpath}` : ""} alt="productImg" fill className="absolute top-0 right-0 h-full w-full object-cover"/> */}

                            <span className={`${manrope.className} font-bold text-4xl capitalize text-black absolute top-[25px] left-9`}>
                                {items?.title ?? null}
                            </span>
                        </div>
                            })
                        }
                    </div>
                </div>
            </div>
            }
        </section>
        </>
    )
}