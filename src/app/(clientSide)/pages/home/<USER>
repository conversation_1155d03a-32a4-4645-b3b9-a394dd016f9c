import Image from "next/image";
import { <PERSON>,<PERSON><PERSON><PERSON> } from "next/font/google";
import TopSell1 from "../../../../../public/topSell1.png";
import TopSell2 from "../../../../../public/topSell2.png";
import TopSell3 from "../../../../../public/topSell3.png";
import TopSell4 from "../../../../../public/topSell4.png";
import { FaStar, FaStarHalf } from "react-icons/fa";

const newArrivals = [
    {
        img:TopSell1,
        title:'vertical striped shirt',
        fullStar:4,
        halfStar:1,
        rating:'4.5/5',
        price:"$212",
        discount:"$232",
        percent:"-20%"
    },
    {
        img:TopSell2,
        title:'courage graphic t-shirt',
        fullStar:3,
        halfStar:1,
        rating:'3.5/5',
        price:"$240",
        discount:null,
        percent:null
    },
    {
        img:TopSell3,
        title:'loose fit bermuda shorts',
        fullStar:4,
        halfStar:1,
        rating:'4.5/5',
        price:"$120",
        discount:null,
        percent:null
    },
    {
        img:TopSell4,
        title:'faded skinny jeans',
        fullStar:4,
        halfStar:1,
        rating:'4.5/5',
        price:"$130",
        discount:"$160",
        percent:"-30%"
    }
]

const anton = Anton({
    subsets:['latin'],
    weight:'400'
});

const manrope = Manrope({
    subsets:['latin']
});

export default function TopSelling(){

    const ratingStar=(fullStar:number,halftStar:number)=>{
        const fullIcon = <FaStar />;
        const halfIcon = <FaStarHalf />;
        const collectionStar = [];
        
        for(let i=0; i<fullStar; i++){
            collectionStar.push(fullIcon)
        }

        for(let a=0; a<halftStar; a++){
            collectionStar.push(halfIcon)
        }

        return collectionStar;
    }
    return(
        <>
        <section className="mt-[64px]">
            <div className="max-w-screen-2xl mx-auto xl:px-[148px] px-5 text-center mb-[55px]">
                <h2 className={`${anton.className} font-bold text-black xl:text-5xl text-[32px] uppercase`}>
                    top selling
                </h2>
            </div>

            <div className="max-w-screen-2xl mx-auto px-5 xl:px-[148px] flex xl:grid xl:grid-cols-4 gap-x-5 overflow-x-auto">
                {
                    newArrivals.map((items,index)=>{
                        return <div key={index} className="w-[80%] shrink-0 xl:w-full">
                            <div className="bg-[#F0EEED] rounded-lg w-full h-[298px] mb-4">
                                <Image src={items.img} alt="productImg" className="h-full w-full"/>
                            </div>

                            <div>
                                <h5 className={`${manrope.className} font-bold text-xl capitalize`}>
                                    {items.title}
                                </h5>

                                <div className="flex flex-row items-center gap-x-2 my-2">
                                    <span className="flex flex-row gap-x-1">
                                        {
                                            ratingStar(items.fullStar,items.halfStar).map((starItems,starIndex)=>{
                                                return <span key={starIndex} className="text-[#FFC633]">
                                                    {starItems}
                                                </span>
                                            })
                                        }
                                    </span>

                                    <span className={`${manrope.className} font-normal text-sm text-black`}>
                                        {items.rating}
                                    </span>
                                </div>

                                <div className="flex flex-row gap-x-[10px] items-center">
                                    <span className={`${manrope.className} font-bold text-2xl`}>
                                        {items.price}
                                    </span>
                                    {
                                        items.discount?
                                        <span className={`${manrope.className} font-bold text-2xl text-[#000000]/40 line-through`}>
                                        {items.discount}
                                        </span>:null
                                    }
                                    
                                    {
                                        items.percent?
                                        <span className={`${manrope.className} font-medium text-xs text-[#FF3333] px-[13.5px] py-[6px] rounded-full bg-[#FF3333]/10`}>
                                        {items.percent}
                                        </span>:null
                                    }
                                </div>
                            </div>
                        </div>
                    })
                }
            </div>

            <div className="max-w-screen-2xl mx-auto xl:px-[148px] px-5 mt-9">
                <div className="w-full flex justify-center pb-16">
                    <button className={`${manrope.className} font-medium text-base py-[15px] px-20 rounded-full capitalize border border-[#000000]/10`}>
                        view all
                    </button>
                </div>
            </div>
        </section>
        </>
    )
}