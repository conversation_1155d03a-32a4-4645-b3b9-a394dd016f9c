import { FaArrowLeft, FaArrowRight, FaCheck, FaRegCheckCircle, FaStar, FaStarHalf } from "react-icons/fa";
import { <PERSON>, Manrope } from "next/font/google";

const customerComment=[
    {
        fullStar:4,
        halfStar:1,
        name:'sarah m.',
        comments:"I'm blown away by the quality and style of the clothes I received from Shop.co. From casual wear to elegant dresses, every piece I've bought has exceeded my expectations.”"
    },
    {
        fullStar:4,
        halfStar:1,
        name:'sarah m.',
        comments:"Finding clothes that align with my personal style used to be a challenge until I discovered Shop.co. The range of options they offer is truly remarkable, catering to a variety of tastes and occasions.”"
    },
    {
        fullStar:4,
        halfStar:1,
        name:'sarah m.',
        comments:"As someone who's always on the lookout for unique fashion pieces, I'm thrilled to have stumbled upon Shop.co. The selection of clothes is not only diverse but also on-point with the latest trends.”"
    },
    {
        fullStar:4,
        halfStar:1,
        name:'sarah m.',
        comments:"I'm blown away by the quality and style of the clothes I received from Shop.co. From casual wear to elegant dresses, every piece I've bought has exceeded my expectations.”"
    },
    {
        fullStar:4,
        halfStar:1,
        name:'sarah m.',
        comments:"I'm blown away by the quality and style of the clothes I received from Shop.co. From casual wear to elegant dresses, every piece I've bought has exceeded my expectations.”"
    }
];

const anton = Anton({
    subsets:['latin'],
    weight:'400'
});

const manrope = Manrope({
    subsets:['latin']
});

export default function HappyCustomer(){
    const ratingStar=(fullStar:number,halfStar:number)=>{
        const fullIcon = <FaStar />;
        const halfIcon = <FaStarHalf />;
        const collectStar=[];

        for(let i=0; i<fullStar; i++){
            collectStar.push(fullIcon)
        }

        for(let a=0; a<halfStar; a++){
            collectStar.push(halfIcon)
        }

        return collectStar;
    }
    return(
        <>
        <section className="mt-20">
            <div className="max-w-screen-2xl mx-auto xl:px-[148px] px-5 xl:text-left text-center xl:mb-0 mb-5">
                <h3 className={`${anton.className} font-bold text-black xl:text-5xl text-[32px] uppercase`}>
                    our happy customers
                </h3>
            </div>
            <div className="max-w-screen-2xl mx-auto xl:px-[148px] px-5 flex flex-row xl:justify-end justify-center gap-x-[18.63px]">
                <span>
                    <FaArrowLeft />
                </span>
                <span>
                    <FaArrowRight />
                </span>
            </div>

            <div className="max-w-screen-2xl mx-auto mt-[44px] relative xl:px-0 px-5">
                <span className="h-full w-[10%] absolute bg-white/50 left-0 rounded-lg xl:block hidden"></span>

                <span className="h-full w-[10%] absolute bg-white/50 right-0 rounded-lg xl:block hidden"></span>

                <div className="flex flex-row justify-center gap-x-5 overflow-x-auto hideScrollbar ">
                    {
                    customerComment.map((items,index)=>{
                        return <div key={index} className="py-7 px-8 xl:w-[25%] w-full shrink-0 border border-[#000000]/10 rounded-lg">
                            <div className="flex flex-row gap-x-2 mb-[19.5px]">
                                {
                                ratingStar(items.fullStar,items.halfStar).map((starItems,starIndex)=>{
                                    return <span className="text-[#FFC633]" key={starIndex}>{starItems}</span>
                                })
                                }
                            </div>

                            <div className="flex flex-row gap-x-2 items-center mb-[16.5px]">
                                <h4 className={`${manrope.className} font-bold text-xl capitalize`}>{items.name}</h4>
                                <span className="h-5 w-5 rounded-full bg-[#01AB31] flex justify-center items-center text-white text-xs">
                                    <FaCheck />
                                </span>
                            </div>

                            <div>
                                <p className={`${manrope.className} text-[#000000]/60 font-normal text-base leading-[22px]`}>
                                    {items.comments}
                                </p>
                            </div>
                        </div>
                    })
                }
                </div>
                
            </div>
        </section>
        </>
    )
}