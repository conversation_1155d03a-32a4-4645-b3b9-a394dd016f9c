import { Manrope } from "next/font/google"
import { MdKeyboardArrowDown } from "react-icons/md";
import { FaStar, FaStarHalf } from "react-icons/fa";
import Products1 from "../../../../../public/products1.png";
import Products2 from "../../../../../public/products2.png";
import Products3 from "../../../../../public/products3.png";
import Image from "next/image";
import { GrFormNextLink, GrFormPreviousLink, GrLinkNext } from "react-icons/gr";

type PropsType = {
    params: string | null
}

const manrope = Manrope({
    subsets:['latin']
})

const dresstorage = [
    {
        image:Products1,
        title:"Gradient Graphic T-shirt",
        rating: 3.5,
        discount: "$240",
        regular : "$260",
        percent : "-20%"
    },
    {
        image:Products2,
        title:"Gradient Graphic T-shirt",
        rating: 3.5,
        discount: "$240",
        regular : "$260",
        percent : "-20%"
    },
    {
        image:Products3,
        title:"Gradient Graphic T-shirt",
        rating: 3.5,
        discount: "$240",
        regular : "$260",
        percent : "-20%"
    },
    {
        image:Products3,
        title:"Gradient Graphic T-shirt",
        rating: 3.5,
        discount: "$240",
        regular : "$260",
        percent : "-20%"
    },
    {
        image:Products2,
        title:"Gradient Graphic T-shirt",
        rating: 3.5,
        discount: "$240",
        regular : "$260",
        percent : "-20%"
    },
    {
        image:Products1,
        title:"Gradient Graphic T-shirt",
        rating: 3.5,
        discount: "$240",
        regular : "$260",
        percent : "-20%"
    },
    {
        image:Products2,
        title:"Gradient Graphic T-shirt",
        rating: 3.5,
        discount: "$240",
        regular : "$260",
        percent : "-20%"
    },
    {
        image:Products3,
        title:"Gradient Graphic T-shirt",
        rating: 3.5,
        discount: "$240",
        regular : "$260",
        percent : "-20%"
    },
    {
        image:Products1,
        title:"Gradient Graphic T-shirt",
        rating: 3.5,
        discount: "$240",
        regular : "$260",
        percent : "-20%"
    }
];

export default function Rightside({params}:PropsType){
    const customizeStar=(rate:number)=>{
        const fullStar = Math.floor(rate);
        const halfStar = rate % fullStar;
        const star = [];

        for(let i=0; i<fullStar; i++){
            star.push(<FaStar key={`fullS${i}`}/>)
        }

        if(halfStar > 0){
            star.push(<FaStarHalf key="half"/>)
        }

        return <div className="flex flex-row gap-x-2 text-[#FFC633]">{star}</div>
    }
    return(
        <>
        <div className="grid grid-cols-3 items-center">
            <div>
                <h2 className={`${manrope} font-bold text-[32px] capitalize text-black`}>
                    {params}
                </h2>
            </div>

            <div className="col-span-2 flex flex-row gap-x-3 justify-end">
                <div>
                    <p className={`${manrope} text-black/60 font-normal text-base capitalize`}>
                        Showing 1-10 of 100 products
                    </p>
                </div>

                <div className="flex flex-row">
                    <div>
                        <p className={`${manrope} text-black/60 font-normal text-base capitalize`}>
                            sort by:
                        </p>
                    </div>

                    <div>
                        <h5 className={`${manrope} text-black font-normal text-base capitalize flex flex-row items-center gap-x-1`}>
                            most popular<span><MdKeyboardArrowDown /></span>
                        </h5>
                    </div>
                </div>
            </div>
        </div>

        <div className="grid grid-cols-3 gap-x-5 gap-y-9 mt-4">
            {
                dresstorage.map((items,index)=>{
                    return <div key={index}>
                        <div className="w-full h-[298px] bg-[#F0EEED] relative rounded-[20px] overflow-hidden">
                            <Image src={items.image} fill alt="productImg" className="h-full w-full object-cover absolute"/>
                        </div>

                        <div className="mt-4">
                            <h3 className={`${manrope} font-bold text-[20px] capitalize text-black`}>
                                {items.title}
                            </h3>

                            <div className="flex flex-row gap-x-3 mt-[18px] mb-2">
                                <div>
                                    {customizeStar(items.rating)}
                                </div>

                                <div className={`${manrope} font-normal text-sm flex flex-row gap-x-1`}>
                                    <span className="text-black">
                                        {items.rating}
                                    </span>
                                    <span className="text-black">
                                        /
                                    </span>
                                    <span className="text-black/60">
                                        5
                                    </span>
                                </div>
                            </div>

                            <div className="flex flex-row gap-x-2">
                                {
                                    items.discount?
                                    <span className={`${manrope} font-bold text-2xl text-black`}>
                                    {items.discount}
                                    </span>:null
                                }
                                
                                {
                                    items.regular?
                                    <span className={`${manrope} font-bold text-2xl text-black/40 line-through`}>
                                    {items.regular}
                                    </span>:null
                                }
                                

                                {
                                    items.rating?
                                    <span className={`${manrope} font-bold text-[12px] text-[#FF3333] py-[6px] px-[13.5px] rounded-[62px] bg-[#FF3333]/10`}>
                                    -{items.rating}%
                                    </span>: null
                                }
                            </div>
                        </div>
                    </div>
                })
            }
        </div>

        <div className="h-[1px] w-full bg-[#000000]/10 my-8"></div>

        <div className="grid grid-cols-6">
            <div>
                <button className={`${manrope} font-medium text-sm capitalize text-black flex flex-row items-center gap-x-2 py-2 px-[14px] rounded-xl border border-black/10`}>
                    <GrFormPreviousLink />
                    Previous
                </button>
            </div>

            <div className="col-span-4">

            </div>

            <div>
                <button className={`${manrope} font-medium text-sm capitalize text-black flex flex-row items-center gap-x-2 py-2 px-[14px] rounded-xl border border-black/10`}>
                    Next
                    <GrFormNextLink />
                </button>
            </div>
        </div>  
        </>
    )
}