import { Manrope } from "next/font/google";
import { ReactNode } from "react";
import { IoIosArrowUp } from "react-icons/io";
import { MdKeyboardArrowRight } from "react-icons/md";
import { RiSoundModuleFill } from "react-icons/ri";

const subcategory = ["T-shirts","Shorts","Shirts","Hoodie","Jeans"];

const colorplate = ["#00C12B","#F50606","#F5DD06","#F57906","#06CAF5","#063AF5","#7D06F5","#F506A4","#FFFFFF","#000000"];

const sizeplate = ["xx-small","x-small","small","medium","large","x-large","xx-large","3x-large","4x-large"];

const dresstyle = ["casual","formal","party","gym"];

const manrope = Manrope({
    subsets:['latin']
})

const customizHeadline = (title:string,icon:ReactNode)=>{
    return <div className="flex flex-row justify-between items-center">
            <div>
                <h4 className={`${manrope} font-bold text-xl capitalize text-[#000000]`}>
                    {title}
                </h4>
            </div>

            <div>
                <span className="text-[#000000]/40">
                    {icon}
                </span>
            </div>
        </div>
}
export default function Leftside(){
    return(
        <>
        {customizHeadline("filters",<RiSoundModuleFill />)}

        <span className="w-full h-[1px] bg-[#000000]/10 block mt-6"></span>

        <div className="flex flex-col gap-y-[25px] mt-[26px]">
            {
                subcategory.map((items,index)=>{
                    return <div className="flex flex-row justify-between" key={index}>
                        <div>
                            <p className={`${manrope} font-normal text-[#000000]/60 text-base`}>
                                {items}
                            </p>
                        </div>
                        <div>
                            <span className="text-[#000000]/60 text-base">
                                <MdKeyboardArrowRight />
                            </span>
                        </div>
                    </div>
                })
            }
        </div>

        <span className="w-full h-[1px] bg-[#000000]/10 block my-[26px]"></span>

        {customizHeadline("price",<IoIosArrowUp />)}

        <div>
            {/* price range will appear here */}
        </div>

        <span className="w-full h-[1px] bg-[#000000]/10 block my-6"></span>

        {customizHeadline("colors",<IoIosArrowUp />)}

        <div className="grid grid-cols-5 gap-x-[15px] gap-y-4 mt-5">
                {
                    colorplate.map((items,index)=>{
                        return <div className="h-[37px] w-[37px] rounded-full shadow-sm shadow-black/50" style={{backgroundColor:`${items}`}} key={index}>

                        </div>
                    })
                }
        </div>

        <span className="w-full h-[1px] bg-[#000000]/10 block my-6"></span>

        {customizHeadline("size",<IoIosArrowUp />)}

        <div className="flex flex-row flex-wrap gap-x-2 gap-y-2 mt-5">
            {
                sizeplate.map((items,index)=>{
                    return <div className="py-[10px] px-5 rounded-full bg-[#F0F0F0]" key={index}>
                        <span className={`${manrope} font-normal text-sm text-[#000000]/60 capitalize`}>
                            {items}
                        </span>
                    </div>
                })
            }
        </div>

        <span className="w-full h-[1px] bg-[#000000]/10 block my-6"></span>

        {customizHeadline("dress style",<IoIosArrowUp />)}

        <div className="flex flex-col gap-y-[22px] mt-5">
            {
                dresstyle.map((items,index)=>{
                    return <div className="flex flex-row justify-between" key={index}>
                        <div>
                            <p className={`${manrope} font-normal text-[#000000]/60 text-base capitalize`}>
                                {items}
                            </p>
                        </div>

                        <div>
                            <span>
                                <MdKeyboardArrowRight />
                            </span>
                        </div>
                    </div>
                })
            }
        </div>

        <div className="mt-[26px]">
            <button className="py-[14px] w-full rounded-full bg-black text-white">
                Apply Filter
            </button>
        </div>
        </>
    )
}